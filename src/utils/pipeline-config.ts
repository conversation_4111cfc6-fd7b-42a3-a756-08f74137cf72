/**
 * Automated Quality Pipeline Configuration
 * English Professional Prompts + Zero Hardcoding + Quality Gates
 * v1.4.0 - Sub-Agents Revolution
 */

export interface PipelineConfig {
  qualityThresholds: QualityThresholds;
  prompts: ProfessionalPrompts;
  optimization: OptimizationConfig;
  pipeline: PipelineSettings;
}

export interface QualityThresholds {
  generation: number;
  execution: number;
  validation: number;
  testing: number;
  overall: number;
}

export interface ProfessionalPrompts {
  specGeneration: PromptTemplate;
  specExecution: PromptTemplate;
  specValidation: PromptTemplate;
  specTesting: PromptTemplate;
  optimization: PromptTemplate;
}

export interface PromptTemplate {
  role: string;
  expertise: string[];
  systemPrompt: string;
  taskPrompt: string;
  qualityStandards: string[];
  outputFormat: string;
}

export interface OptimizationConfig {
  maxAttempts: number;
  improvementStrategies: string[];
  qualityMetrics: string[];
}

export interface PipelineSettings {
  autoRetry: boolean;
  parallelExecution: boolean;
  timeoutSeconds: number;
  logLevel: string;
}

/**
 * Default Pipeline Configuration
 */
export const DEFAULT_PIPELINE_CONFIG: PipelineConfig = {
  qualityThresholds: {
    generation: 95,
    execution: 95,
    validation: 95,
    testing: 95,
    overall: 95
  },

  prompts: {
    specGeneration: {
      role: "Senior Requirements Analyst",
      expertise: [
        "Requirements Engineering",
        "User Story Writing",
        "Acceptance Criteria Definition",
        "Business Process Analysis",
        "Stakeholder Management"
      ],
      systemPrompt: `You are a Senior Requirements Analyst with 15+ years of experience in enterprise software development.

Your expertise includes:
- Deep understanding of business requirements and user needs
- Writing clear, testable, and comprehensive user stories
- Defining precise acceptance criteria using Given/When/Then format
- Identifying edge cases and non-functional requirements
- Ensuring requirements traceability and completeness

Your working style is methodical, detail-oriented, and user-centric. You always consider the business value and technical feasibility of each requirement.`,

      taskPrompt: `Analyze the following project description and generate comprehensive requirements specification:

**Project Description**: {description}
**Quality Target**: {qualityThreshold}%

Generate a complete requirements specification that includes:

1. **Project Overview**
   - Business objectives and success criteria
   - Target user personas and use cases
   - Key stakeholders and their needs

2. **Functional Requirements**
   - Core user stories in EARS format (As a... I want... So that...)
   - Detailed acceptance criteria (Given/When/Then)
   - User interface requirements
   - Data requirements

3. **Non-Functional Requirements**
   - Performance requirements (response time, throughput)
   - Security requirements (authentication, authorization, data protection)
   - Usability requirements (accessibility, user experience)
   - Scalability and reliability requirements

4. **Technical Constraints**
   - Technology stack preferences
   - Integration requirements
   - Compliance requirements

5. **Risk Assessment**
   - Technical risks and mitigation strategies
   - Business risks and contingency plans
   - Dependencies and assumptions

Ensure all requirements are:
- Specific, Measurable, Achievable, Relevant, Time-bound (SMART)
- Testable and verifiable
- Prioritized using MoSCoW method
- Traceable to business objectives`,

      qualityStandards: [
        "All user stories must follow EARS format",
        "Acceptance criteria must use Given/When/Then structure",
        "Requirements must be testable and verifiable",
        "Non-functional requirements must be quantified",
        "Risk assessment must be comprehensive",
        "Requirements must be prioritized and traceable"
      ],

      outputFormat: "Structured markdown with clear sections and subsections"
    },

    specExecution: {
      role: "Senior Software Architect",
      expertise: [
        "System Architecture Design",
        "Technology Stack Selection",
        "API Design",
        "Database Design",
        "Security Architecture",
        "Performance Optimization"
      ],
      systemPrompt: `You are a Senior Software Architect with 15+ years of experience in designing scalable enterprise systems.

Your expertise includes:
- Designing high-availability, high-performance system architectures
- Selecting optimal technology stacks and frameworks
- Creating comprehensive API specifications
- Designing efficient database schemas
- Implementing security best practices
- Optimizing system performance and scalability

Your approach is pragmatic, focusing on proven patterns and industry best practices while considering future scalability and maintainability.`,

      taskPrompt: `Based on the requirements specification, create a comprehensive system design:

**Requirements**: {requirements}
**Quality Target**: {qualityThreshold}%

Generate a complete system design that includes:

1. **System Architecture**
   - High-level architecture diagram
   - Component breakdown and responsibilities
   - Data flow and communication patterns
   - Deployment architecture

2. **Technology Stack**
   - Frontend technologies and frameworks
   - Backend technologies and frameworks
   - Database selection and rationale
   - Third-party services and integrations

3. **API Design**
   - RESTful API specifications
   - Authentication and authorization mechanisms
   - Request/response formats
   - Error handling strategies

4. **Database Design**
   - Entity relationship diagrams
   - Table schemas and indexes
   - Data migration strategies
   - Backup and recovery plans

5. **Security Design**
   - Authentication and authorization flows
   - Data encryption strategies
   - Security monitoring and logging
   - Compliance considerations

6. **Performance Considerations**
   - Caching strategies
   - Load balancing approaches
   - Database optimization
   - Monitoring and alerting

Ensure the design is:
- Scalable and maintainable
- Secure and compliant
- Performance-optimized
- Technology-agnostic where possible`,

      qualityStandards: [
        "Architecture must support all functional requirements",
        "Technology choices must be justified",
        "API design must follow RESTful principles",
        "Database design must be normalized",
        "Security measures must be comprehensive",
        "Performance considerations must be addressed"
      ],

      outputFormat: "Structured markdown with diagrams and technical specifications"
    },

    specValidation: {
      role: "Senior Quality Assurance Engineer",
      expertise: [
        "Quality Assessment",
        "Requirements Validation",
        "Test Strategy Design",
        "Risk Analysis",
        "Process Improvement",
        "Standards Compliance"
      ],
      systemPrompt: `You are a Senior Quality Assurance Engineer with 12+ years of experience in software quality management.

Your expertise includes:
- Comprehensive quality assessment methodologies
- Requirements validation and verification
- Test strategy design and implementation
- Risk analysis and mitigation planning
- Process improvement and optimization
- Standards compliance and auditing

Your approach is systematic, thorough, and focused on preventing defects rather than just detecting them.`,

      taskPrompt: `Validate the quality of the generated specifications and design:

**Specifications**: {specifications}
**Design**: {design}
**Quality Target**: {qualityThreshold}%

Perform comprehensive quality validation including:

1. **Requirements Quality Assessment**
   - Completeness and consistency check
   - Testability and verifiability analysis
   - Traceability validation
   - Ambiguity detection

2. **Design Quality Assessment**
   - Architecture consistency with requirements
   - Technology stack appropriateness
   - Security and performance considerations
   - Maintainability and scalability evaluation

3. **Quality Metrics Calculation**
   - Requirements coverage score
   - Design completeness score
   - Technical risk assessment
   - Overall quality score

4. **Gap Analysis**
   - Missing requirements identification
   - Design gaps and inconsistencies
   - Risk areas and mitigation needs
   - Improvement recommendations

5. **Compliance Check**
   - Industry standards compliance
   - Best practices adherence
   - Security standards validation
   - Performance benchmarks

Provide:
- Detailed quality scores for each dimension
- Specific improvement recommendations
- Risk assessment and mitigation strategies
- Overall quality rating and justification`,

      qualityStandards: [
        "Quality assessment must be objective and measurable",
        "All gaps and issues must be clearly identified",
        "Recommendations must be specific and actionable",
        "Risk assessment must be comprehensive",
        "Quality scores must be justified",
        "Compliance check must be thorough"
      ],

      outputFormat: "Structured quality report with scores, gaps, and recommendations"
    },

    specTesting: {
      role: "Senior Test Engineer",
      expertise: [
        "Test Strategy Design",
        "Test Case Development",
        "Automated Testing",
        "Performance Testing",
        "Security Testing",
        "Quality Metrics"
      ],
      systemPrompt: `You are a Senior Test Engineer with 12+ years of experience in comprehensive software testing.

Your expertise includes:
- Designing comprehensive test strategies
- Developing detailed test cases and scenarios
- Implementing automated testing frameworks
- Conducting performance and load testing
- Performing security testing and vulnerability assessment
- Establishing quality metrics and reporting

Your approach is risk-based, focusing on critical paths and high-impact scenarios while ensuring comprehensive coverage.`,

      taskPrompt: `Create a comprehensive test strategy and validation plan:

**Specifications**: {specifications}
**Design**: {design}
**Quality Target**: {qualityThreshold}%

Generate a complete test strategy including:

1. **Test Strategy Overview**
   - Testing objectives and scope
   - Test levels and types
   - Entry and exit criteria
   - Risk-based testing approach

2. **Test Case Design**
   - Functional test scenarios
   - Non-functional test cases
   - Edge cases and boundary conditions
   - Negative testing scenarios

3. **Automated Testing Plan**
   - Unit testing strategy
   - Integration testing approach
   - End-to-end testing framework
   - Continuous testing pipeline

4. **Performance Testing**
   - Load testing scenarios
   - Stress testing conditions
   - Performance benchmarks
   - Monitoring and alerting

5. **Security Testing**
   - Vulnerability assessment plan
   - Penetration testing scenarios
   - Security compliance validation
   - Data protection testing

6. **Quality Metrics**
   - Test coverage metrics
   - Defect tracking and analysis
   - Quality gates and thresholds
   - Reporting and dashboards

Ensure the test strategy:
- Covers all requirements and design elements
- Includes appropriate automation
- Addresses performance and security
- Provides measurable quality metrics`,

      qualityStandards: [
        "Test strategy must cover all requirements",
        "Test cases must be detailed and executable",
        "Automation strategy must be comprehensive",
        "Performance testing must be realistic",
        "Security testing must be thorough",
        "Quality metrics must be measurable"
      ],

      outputFormat: "Comprehensive test strategy document with detailed test cases"
    },

    optimization: {
      role: "Senior Process Improvement Specialist",
      expertise: [
        "Process Optimization",
        "Quality Improvement",
        "Root Cause Analysis",
        "Best Practices Implementation",
        "Continuous Improvement",
        "Performance Enhancement"
      ],
      systemPrompt: `You are a Senior Process Improvement Specialist with 10+ years of experience in optimizing software development processes.

Your expertise includes:
- Identifying process inefficiencies and bottlenecks
- Implementing quality improvement strategies
- Conducting root cause analysis
- Establishing best practices and standards
- Driving continuous improvement initiatives
- Enhancing team performance and productivity

Your approach is data-driven, focusing on measurable improvements and sustainable process enhancements.`,

      taskPrompt: `Analyze the quality gaps and provide optimization recommendations:

**Current Quality Score**: {currentScore}%
**Target Quality Score**: {targetScore}%
**Quality Gaps**: {qualityGaps}
**Previous Attempts**: {previousAttempts}

Provide optimization strategy including:

1. **Gap Analysis**
   - Root cause identification
   - Impact assessment
   - Priority ranking

2. **Improvement Strategy**
   - Specific improvement actions
   - Implementation timeline
   - Resource requirements

3. **Quality Enhancement**
   - Process improvements
   - Best practices implementation
   - Quality control measures

4. **Risk Mitigation**
   - Risk identification
   - Mitigation strategies
   - Contingency plans

Focus on:
- Addressing the most critical gaps first
- Providing actionable recommendations
- Ensuring sustainable improvements
- Minimizing implementation risks`,

      qualityStandards: [
        "Analysis must be thorough and objective",
        "Recommendations must be specific and actionable",
        "Improvements must be measurable",
        "Implementation must be feasible",
        "Risk mitigation must be comprehensive",
        "Strategy must be sustainable"
      ],

      outputFormat: "Detailed optimization plan with specific actions and timelines"
    }
  },

  optimization: {
    maxAttempts: 3,
    improvementStrategies: [
      "Requirements refinement and clarification",
      "Design optimization and simplification",
      "Risk mitigation and contingency planning",
      "Quality standards enhancement",
      "Process improvement and automation",
      "Best practices implementation"
    ],
    qualityMetrics: [
      "Requirements completeness",
      "Design consistency",
      "Technical feasibility",
      "Security compliance",
      "Performance adequacy",
      "Maintainability score"
    ]
  },

  pipeline: {
    autoRetry: true,
    parallelExecution: false,
    timeoutSeconds: 300,
    logLevel: "info"
  }
};

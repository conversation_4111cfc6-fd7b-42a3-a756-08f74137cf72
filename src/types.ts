/**
 * Type definitions for Vibe Coding workflow system
 */

export interface SpecConfig {
  name: string;
  title: string;
  status: 'created' | 'requirements' | 'design' | 'tasks' | 'implementing' | 'completed';
  createdAt: string;
  updatedAt: string;
  currentTask?: number;
}

export interface BugConfig {
  name: string;
  title: string;
  status: 'reported' | 'analyzing' | 'fixing' | 'verifying' | 'resolved';
  createdAt: string;
  updatedAt: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

export interface SubAgentsConfig {
  qualityThreshold: number;
  outputFormat: string;
  autoRestart: boolean;
  maxRetries: number;
  maxWorkflowRetries: number;
  enabledAgents: string[];
  phaseThresholds: Record<string, number>;
  saveResults: boolean;
  showProgress: boolean;
}

export interface WorkflowConfig {
  specs: Record<string, SpecConfig>;
  bugs: Record<string, BugConfig>;
  subAgents?: SubAgentsConfig;
}

export type SeverityLevel = 'low' | 'medium' | 'high' | 'critical';
export type SpecStatus = 'created' | 'requirements' | 'design' | 'tasks' | 'implementing' | 'completed';
export type BugStatus = 'reported' | 'analyzing' | 'fixing' | 'verifying' | 'resolved';
export type SteeringType = 'all' | 'product' | 'tech' | 'structure';

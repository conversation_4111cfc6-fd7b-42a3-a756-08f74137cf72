{"name": "vibe-coding", "version": "1.3.0", "keywords": ["MCP", "codelf", "vibe coding", "AI IDE", "cursor", "claude", "ai assistant", "ai agent", "ai editor"], "repository": {"type": "git", "url": "https://github.com/xwfe/vibe-coding"}, "bin": {"codelf": "build/index.js"}, "files": ["build"], "license": "MIT", "author": "xwfe", "description": "", "dependencies": {"@modelcontextprotocol/sdk": "^1.5.0", "@react-email/components": "^0.0.33", "@react-email/render": "^1.0.5", "@types/gitignore-parser": "^0.0.3", "gitignore-parser": "^0.0.2", "minimist": "^1.2.8", "resend": "^4.1.2", "rolldown": "1.0.0-beta.30", "zod": "^3.24.2"}, "devDependencies": {"@rollup/plugin-commonjs": "^28.0.6", "@rollup/plugin-node-resolve": "^16.0.1", "@types/minimist": "^1.2.5", "@types/node": "^22.13.5", "@types/react": "^18.2.64", "@types/react-dom": "^18.2.21", "rollup-plugin-string": "^3.0.0", "typescript": "^5.8.2"}, "type": "module", "scripts": {"build": "rolldown -c rolldown.config.js && node -e \"require('fs').chmodSync('build/index.js', '755')\"", "build:tsc": "tsc && node -e \"require('fs').chmodSync('build/index.js', '755')\"", "dev": "rolldown -c rolldown.config.js --watch", "dev:tsc": "tsc --watch", "start": "node build/index.js"}}
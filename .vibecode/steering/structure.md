# Project Structure

## File Organization Pattern
```
.vibecode/
├── steering/           # Steering documents
├── workflows/          # Workflow processes
├── templates/          # Template files
└── *.md               # Project documents
```

## Naming Conventions
- File names: kebab-case
- Variable names: camelCase
- Class names: PascalCase

## Import Patterns
- Use relative paths for importing local modules
- Organize import statements alphabetically

## Code Organization Principles
- Single Responsibility Principle
- Modular design
- Clear interface definitions

---
*This document was automatically generated by vibe-coding workflow, please update according to actual project conditions*

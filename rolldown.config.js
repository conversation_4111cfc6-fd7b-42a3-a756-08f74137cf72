import { defineConfig } from 'rolldown';
import { string } from 'rollup-plugin-string';

export default defineConfig({
  input: 'src/index.ts',
  output: {
    dir: 'build',
    format: 'esm',
    entryFileNames: '[name].js',
    chunkFileNames: '[name]-[hash].js'
  },
  plugins: [
    // Handle template files as raw strings
    string({
      include: [
        'templates/**/*.template',
        'templates/**/*.hbs',
        'templates/**/*.mustache',
        '.vibecode/templates/**/*.template',
        '.vibecode/templates/**/*.hbs',
        '.vibecode/templates/**/*.mustache'
      ]
    })
  ],

  external: [
    // Node.js built-ins
    'fs',
    'fs/promises',
    'path',
    'url',
    'util',
    'events',
    'stream',
    'crypto',
    'os',
    'child_process',
    'node:fs',
    'node:path',
    'node:url',
    'node:util',
    'node:events',
    'node:stream',
    'node:crypto',
    'node:os',
    'node:child_process',
    'node:process',

    // Keep these as external dependencies
    '@modelcontextprotocol/sdk',
    '@modelcontextprotocol/sdk/server/mcp.js',
    '@modelcontextprotocol/sdk/server/stdio.js',
    'zod',
    'minimist',
    'gitignore-parser',
    'resend',
    '@react-email/components',
    '@react-email/render',
    'typescript'
  ],

  // Tree shaking
  treeshake: {
    moduleSideEffects: false
  }
});

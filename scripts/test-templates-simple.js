#!/usr/bin/env node

/**
 * Simple Template Test
 * Tests the template system directly without MCP server
 */

import { templateManager } from '../build/index.js';

async function testTemplates() {
  console.log('🎨 Testing Rolldown Template System');
  console.log('='.repeat(50));

  try {
    // Test template manager
    const templates = templateManager.listTemplates();
    console.log(`✅ Found ${templates.length} templates:`);
    templates.forEach(t => console.log(`  • ${t}`));
    console.log();

    // Test TypeScript class generation
    const classData = {
      className: 'UserService',
      description: 'Service for managing user operations',
      properties: [
        { name: 'apiClient', type: 'ApiClient', isPrivate: true }
      ],
      methods: [
        {
          name: 'getUser',
          parameters: [{ name: 'id', type: 'string' }],
          returnType: 'Promise<User>',
          isAsync: true
        }
      ],
      constructorParams: [
        { name: 'apiClient', type: 'ApiClient' }
      ]
    };

    const generatedClass = templateManager.render('typescript-class', classData);
    console.log('✅ TypeScript class template rendered:');
    console.log('-'.repeat(40));
    console.log(generatedClass);
    console.log('-'.repeat(40));
    console.log();

    // Test React component
    const componentData = {
      name: 'UserProfile',
      description: 'User profile component',
      props: [
        { name: 'user', type: 'User', isOptional: false }
      ],
      hasState: false,
      hasEffects: false
    };

    const generatedComponent = templateManager.render('react-component', componentData);
    console.log('✅ React component template rendered:');
    console.log('-'.repeat(40));
    console.log(generatedComponent.substring(0, 500) + '...');
    console.log('-'.repeat(40));
    console.log();

    console.log('🎉 All tests passed! Rolldown + Templates working perfectly!');

  } catch (error) {
    console.error('❌ Test failed:', error);
    process.exit(1);
  }
}

testTemplates();

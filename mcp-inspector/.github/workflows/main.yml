on:
  push:
    branches:
      - main

  pull_request:
  release:
    types: [published]

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: Check formatting
        run: npx prettier --check .

      - uses: actions/setup-node@v4
        with:
          node-version-file: package.json
          cache: npm

      # Working around https://github.com/npm/cli/issues/4828
      # - run: npm ci
      - run: npm install --no-package-lock

      - name: Check version consistency
        run: npm run check-version

      - name: Check linting
        working-directory: ./client
        run: npm run lint

      - name: Run client tests
        working-directory: ./client
        run: npm test

      - run: npm run build

  publish:
    runs-on: ubuntu-latest
    if: github.event_name == 'release'
    environment: release
    needs: build

    permissions:
      contents: read
      id-token: write

    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version-file: package.json
          cache: npm
          registry-url: "https://registry.npmjs.org"

      # Working around https://github.com/npm/cli/issues/4828
      # - run: npm ci
      - run: npm install --no-package-lock

      # TODO: Add --provenance once the repo is public
      - run: npm run publish-all
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}

  publish-github-container-registry:
    runs-on: ubuntu-latest
    if: github.event_name == 'release'
    environment: release
    needs: build
    permissions:
      contents: read
      packages: write
      attestations: write
      id-token: write
    steps:
      - uses: actions/checkout@v4

      - name: Log in to the Container registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata (tags, labels) for Docker
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ghcr.io/${{ github.repository }}

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Build and push Docker image
        id: push
        uses: docker/build-push-action@v6
        with:
          context: .
          push: true
          platforms: linux/amd64,linux/arm64
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}

      - name: Generate artifact attestation
        uses: actions/attest-build-provenance@v2
        with:
          subject-name: ghcr.io/${{ github.repository }}
          subject-digest: ${{ steps.push.outputs.digest }}
          push-to-registry: true

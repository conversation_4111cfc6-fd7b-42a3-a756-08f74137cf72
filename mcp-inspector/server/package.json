{"name": "@modelcontextprotocol/inspector-server", "version": "0.16.2", "description": "Server-side application for the Model Context Protocol inspector", "license": "MIT", "author": "An<PERSON><PERSON>, PBC (https://anthropic.com)", "homepage": "https://modelcontextprotocol.io", "bugs": "https://github.com/modelcontextprotocol/inspector/issues", "type": "module", "bin": {"mcp-inspector-server": "build/index.js"}, "files": ["build"], "scripts": {"build": "tsc", "start": "node build/index.js", "dev": "tsx watch --clear-screen=false src/index.ts", "dev:windows": "tsx watch --clear-screen=false src/index.ts < NUL"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/ws": "^8.5.12", "tsx": "^4.19.0", "typescript": "^5.6.2"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.17.0", "cors": "^2.8.5", "express": "^5.1.0", "ws": "^8.18.0", "zod": "^3.23.8"}}
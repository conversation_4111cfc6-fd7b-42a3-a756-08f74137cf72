// The parsed query parameters returned by the Authorization Server
// representing either a valid authorization_code or an error
// ref: https://datatracker.ietf.org/doc/html/draft-ietf-oauth-v2-1-12#section-4.1.2
type CallbackParams =
  | {
      successful: true;
      // The authorization code is generated by the authorization server.
      code: string;
    }
  | {
      successful: false;
      // The OAuth 2.1 Error Code.
      // Usually one of:
      //    ```
      //    invalid_request, unauthorized_client, access_denied, unsupported_response_type,
      //    invalid_scope, server_error, temporarily_unavailable
      //    ```
      error: string;
      // Human-readable ASCII text providing additional information, used to assist the
      // developer in understanding the error that occurred.
      error_description: string | null;
      // A URI identifying a human-readable web page with information about the error,
      // used to provide the client developer with additional information about the error.
      error_uri: string | null;
    };

export const parseOAuthCallbackParams = (location: string): CallbackParams => {
  const params = new URLSearchParams(location);

  const code = params.get("code");
  if (code) {
    return { successful: true, code };
  }

  const error = params.get("error");
  const error_description = params.get("error_description");
  const error_uri = params.get("error_uri");

  if (error) {
    return { successful: false, error, error_description, error_uri };
  }

  return {
    successful: false,
    error: "invalid_request",
    error_description: "Missing code or error in response",
    error_uri: null,
  };
};

export const generateOAuthErrorDescription = (
  params: Extract<CallbackParams, { successful: false }>,
): string => {
  const error = params.error;
  const errorDescription = params.error_description;
  const errorUri = params.error_uri;

  return [
    `Error: ${error}.`,
    errorDescription ? `Details: ${errorDescription}.` : "",
    errorUri ? `More info: ${errorUri}.` : "",
  ]
    .filter(Boolean)
    .join("\n");
};
